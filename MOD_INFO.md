# 🎮 Advanced Kit Management System for Palworld

**Owner:** KakashixSilent  
**Version:** 2.0  
**Compatible with:** Palworld Dedicated Server + UE4SS  
**Last Updated:** 2025-01-07  

---

## 📖 Description

The Advanced Kit Management System is a comprehensive server-side mod for Palworld that provides administrators with powerful tools to manage and distribute item kits to players. This mod features an intelligent auto-grant system, level-based kit unlocking, cooldown management, and extensive admin controls for creating and managing custom kits.

### ✨ Key Features

- **🎁 Auto-Grant System** - Automatically gives kits to players when they reach required levels
- **📊 Level-Based Access** - Kits unlock based on player progression
- **⏰ Cooldown Management** - Prevents kit spam with configurable time restrictions
- **🛠️ Admin Tools** - Complete kit creation, editing, and management system
- **💾 Live Configuration** - Reload settings without server restart
- **📋 Player Tracking** - Track kit claims, usage statistics, and cooldowns
- **🔧 Easy Customization** - JSON-based configuration for easy editing

---

## 🚀 Installation Process

### Prerequisites
1. **Palworld Dedicated Server** - Running and accessible
2. **UE4SS** - Installed and configured on your server
3. **Admin Access** - To the server files and in-game admin privileges

### Step-by-Step Installation

1. **Download the Mod**
   - Extract all files to your UE4SS mods directory
   - Path: `[PalServer]/Pal/Binaries/Win64/ue4ss/Mods/starterkit/`

2. **File Structure** (Your directory should look like this):
   ```
   starterkit/
   ├── enabled.txt
   ├── MOD_INFO.md
   ├── README.md
   ├── kit_config_template.json
   └── Scripts/
       ├── main.lua
       ├── kit.lua
       ├── kit_manager.lua
       ├── messages.lua
       ├── kit_config.json (auto-generated)
       ├── player_data.json (auto-generated)
       └── libs/
           ├── json.lua
           └── fguid.lua
   ```

3. **Enable the Mod**
   - Ensure `enabled.txt` exists in the mod directory
   - The file should contain just: `1`

4. **Start Your Server**
   - Launch your Palworld dedicated server
   - Check console for: `[Advanced Kit Manager] Loaded successfully!`

5. **Verify Installation**
   - Join your server
   - Type `!kithelp` in chat
   - You should see the help menu

---

## 🎯 Available Kits

| Kit Name | Level Required | Cooldown | Auto-Grant | Description |
|----------|----------------|----------|------------|-------------|
| **starterkit** | 1 | None | ✅ Yes | Basic survival items for new players |
| **pvpkit** | 2 | 2 hours | ✅ Yes | Combat gear for PvP battles |
| **buildkit** | 4 | 1 hour | ✅ Yes | Construction materials and tools |
| **farmkit** | 8 | 30 minutes | ✅ Yes | Agricultural supplies and seeds |
| **miningkit** | 15 | 1.5 hours | ✅ Yes | Advanced mining tools and equipment |

---

## 📋 Commands Reference

### 🎮 Player Commands (Available to Everyone)

#### `!kit <kitname>`
**Description:** Claim a specific kit  
**Usage:** `!kit [kitname]`  
**Examples:**
- `!kit starterkit` - Claims the starter kit
- `!kit pvpkit` - Claims the PvP kit
- `!kit buildkit` - Claims the builder kit

**Requirements:** Must meet level requirement and cooldown period

#### `!kitlist`
**Description:** Display all available kits  
**Usage:** `!kitlist`  
**Example:** `!kitlist`  
**Shows:** List of all kits you can access

#### `!kitinfo <kitname>`
**Description:** View detailed information about a specific kit  
**Usage:** `!kitinfo [kitname]`  
**Examples:**
- `!kitinfo starterkit` - Shows starter kit details
- `!kitinfo pvpkit` - Shows PvP kit requirements and contents

**Shows:** Level requirement, cooldown time, your claim status, items included

#### `!kithelp`
**Description:** Display help information and available commands  
**Usage:** `!kithelp`  
**Example:** `!kithelp`  
**Shows:** All commands available based on your permission level

---

### 🛠️ Admin Commands (Admin Only)

#### `!additem <kitname> <itemid> <quantity>`
**Description:** Add an item to an existing kit  
**Usage:** `!additem [kitname] [itemid] [quantity]`  
**Examples:**
- `!additem starterkit Sword_Tier_01 1` - Adds 1 metal sword to starter kit
- `!additem pvpkit Ammo_AssaultRifle_Default 200` - Adds 200 rifle ammo to PvP kit
- `!additem buildkit Wood 1000` - Adds 1000 wood to builder kit

**Note:** Creates new item entry or updates quantity if item already exists

#### `!removeitem <kitname> <itemid>`
**Description:** Remove an item from a kit  
**Usage:** `!removeitem [kitname] [itemid]`  
**Examples:**
- `!removeitem starterkit Bat` - Removes wooden bat from starter kit
- `!removeitem pvpkit HandGrenade` - Removes grenades from PvP kit

**Note:** Completely removes the item from the kit configuration

#### `!kitpreview <kitname>`
**Description:** Preview all items in a kit with quantities  
**Usage:** `!kitpreview [kitname]`  
**Examples:**
- `!kitpreview starterkit` - Shows all items in starter kit
- `!kitpreview buildkit` - Shows all construction materials

**Shows:** Complete itemized list with quantities

#### `!createkit <kitname>`
**Description:** Create a new empty kit  
**Usage:** `!createkit [kitname]`  
**Examples:**
- `!createkit vipkit` - Creates a new VIP kit
- `!createkit eventkit` - Creates a special event kit

**Default Settings:** Level 1, 1 hour cooldown, auto-grant enabled, empty items list

#### `!givekit <player> <kitname>`
**Description:** Give a kit directly to a specific player  
**Usage:** `!givekit [playername] [kitname]`  
**Examples:**
- `!givekit Kakashi starterkit` - Gives starter kit to Kakashi
- `!givekit PlayerName pvpkit` - Gives PvP kit to PlayerName

**Note:** Bypasses level requirements and cooldown restrictions

#### `!kitreload`
**Description:** Reload kit configuration from files  
**Usage:** `!kitreload`  
**Example:** `!kitreload`  
**Effect:** Refreshes all kit data and settings without server restart

---

## ⚙️ Configuration

### Kit Configuration File: `Scripts/kit_config.json`

The mod uses a JSON-based configuration system for easy customization:

```json
{
  "settings": {
    "auto_save": true,
    "save_interval": 300,
    "max_kits_per_player": 50,
    "default_cooldown": 3600,
    "level_check_enabled": true
  },
  "kits": {
    "kitname": {
      "name": "Display Name",
      "description": "Kit description",
      "level_required": 1,
      "cooldown": 3600,
      "max_claims": -1,
      "auto_grant": true,
      "items": {
        "ItemID": quantity
      }
    }
  }
}
```

### Kit Properties Explained

- **name:** Display name shown to players
- **description:** Brief description of the kit's purpose
- **level_required:** Minimum player level to access the kit
- **cooldown:** Time in seconds between claims (0 = no cooldown)
- **max_claims:** Maximum times a player can claim (-1 = unlimited)
- **auto_grant:** Whether to automatically give kit when level is reached
- **items:** Object containing ItemID -> quantity mappings

---

## 🔧 Troubleshooting

### Common Issues

**Commands not working:**
- Ensure you're using `!` prefix (not `/`)
- Check if mod is properly loaded in console
- Verify `enabled.txt` contains `1`

**Kits not auto-granting:**
- Check player level requirements
- Verify `auto_grant: true` in kit configuration
- Look for console messages about kit grants

**Permission errors:**
- Admin commands require server admin privileges
- Use `/adminpassword [password]` to gain admin access
- Check server configuration for admin settings

**Items not received:**
- Ensure player inventory has space
- Check for typos in item IDs
- Verify items exist in Palworld

### Console Messages

**Success Messages:**
- `[Advanced Kit Manager] Loaded successfully!`
- `[KitManager] Gave [kitname] kit to [player]`
- `[KitManager] Initialized successfully`

**Error Indicators:**
- `lua_pcall returned LUA_ERRRUN`
- `Error executing hook pre-callback`
- `attempt to index a nil value`

---

## 📞 Support & Contact

**Mod Owner:** KakashixSilent  
**Support:** Check server console for error messages  
**Updates:** Monitor for new versions and features  

### Reporting Issues
When reporting issues, please include:
1. Server console output
2. Exact command used
3. Player level and admin status
4. Expected vs actual behavior

---

## 📄 License & Credits

**Created by:** KakashixSilent  
**Based on:** UE4SS Lua modding framework  
**Compatible with:** Palworld Dedicated Server  

This mod is provided as-is for server administration purposes. Use responsibly and ensure fair gameplay for all players.

---

**Version History:**
- v2.0 - Complete rewrite with advanced features
- v1.0 - Basic starter kit functionality
