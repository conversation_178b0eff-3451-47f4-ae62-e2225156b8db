local Messages = {}

-- Simplified messages without color codes
Messages.Colors = {
    SUCCESS = "",
    ERROR = "",
    WARNING = "",
    INFO = "",
    ADMIN = "",
    RESET = ""
}

-- Message templates
Messages.Templates = {
    -- Success messages
    KIT_CLAIMED = "%s%s successfully claimed the '%s' kit!%s",
    KIT_GIVEN = "%sKit '%s' given to player %s%s",
    ITEM_ADDED = "%sAdded %dx %s to kit '%s'%s",
    ITEM_REMOVED = "%sRemoved %s from kit '%s'%s",
    KIT_CREATED = "%sKit '%s' created successfully%s",
    KIT_DELETED = "%sKit '%s' deleted successfully%s",
    CONFIG_RELOADED = "%sKit configuration reloaded successfully%s",
    
    -- Error messages
    NO_PERMISSION = "%sYou do not have permission to use this command%s",
    PLAYER_NOT_FOUND = "%sPlayer '%s' not found or not online%s",
    KIT_NOT_FOUND = "%sKit '%s' does not exist%s",
    KIT_EXISTS = "%sKit '%s' already exists%s",
    ITEM_NOT_FOUND = "%sItem '%s' not found in kit '%s'%s",
    INVALID_QUANTITY = "%sInvalid quantity. Must be a positive number%s",
    INVALID_SYNTAX = "%sInvalid command syntax. Use: %s%s",
    INVENTORY_FULL = "%sCannot give kit - inventory is full%s",
    
    -- Warning messages
    KIT_COOLDOWN = "%sKit '%s' is on cooldown. Time remaining: %s%s",
    LEVEL_REQUIRED = "%sKit '%s' requires level %d (you are level %d)%s",
    MAX_CLAIMS = "%sYou have reached the maximum claims for kit '%s'%s",
    
    -- Info messages
    KIT_INFO = "%sKit: %s | Level: %d | Cooldown: %s | Claims: %d/%s%s",
    PLAYER_STATS = "%sPlayer %s | Level: %d | Total Claims: %d%s",
    AVAILABLE_KITS = "%sAvailable kits: %s%s",
    
    -- Help messages
    HELP_HEADER = "%s=== Kit Management Commands ===%s",
    HELP_PLAYER = "%s!kit <kitname> - Claim a kit%s",
    HELP_LIST = "%s!kitlist - List available kits%s",
    HELP_INFO = "%s!kitinfo <kitname> - View kit information%s",
    HELP_ADMIN_HEADER = "%s=== Admin Commands ===%s",
    HELP_ADDITEM = "%s!additem <kitname> <itemid> <quantity> - Add item to kit%s",
    HELP_REMOVEITEM = "%s!removeitem <kitname> <itemid> - Remove item from kit%s",
    HELP_KITPREVIEW = "%s!kitpreview <kitname> - Preview kit contents%s",
    HELP_CREATEKIT = "%s!createkit <kitname> - Create new kit%s",
    HELP_DELETEKIT = "%s!deletekit <kitname> - Delete kit%s",
    HELP_GIVEKIT = "%s!givekit <player> <kitname> - Give kit to player%s",
    HELP_RELOAD = "%s!kitreload - Reload kit configuration%s",
    HELP_SETLEVEL = "%s!setlevel <player> <level> - Set player level%s"
}

-- Format time duration
function Messages:FormatTime(seconds)
    if seconds <= 0 then
        return "No cooldown"
    end
    
    local hours = math.floor(seconds / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    local secs = seconds % 60
    
    local parts = {}
    if hours > 0 then table.insert(parts, hours .. "h") end
    if minutes > 0 then table.insert(parts, minutes .. "m") end
    if secs > 0 then table.insert(parts, secs .. "s") end
    
    return table.concat(parts, " ")
end

-- Format max claims
function Messages:FormatMaxClaims(maxClaims)
    return maxClaims == -1 and "Unlimited" or tostring(maxClaims)
end

-- Create formatted message
function Messages:Format(template, ...)
    local args = {...}
    return string.format(template, table.unpack(args))
end

-- Success messages
function Messages:KitClaimed(playerName, kitName)
    return self:Format(self.Templates.KIT_CLAIMED, 
        self.Colors.SUCCESS, playerName, kitName, self.Colors.RESET)
end

function Messages:KitGiven(kitName, playerName)
    return self:Format(self.Templates.KIT_GIVEN, 
        self.Colors.SUCCESS, kitName, playerName, self.Colors.RESET)
end

function Messages:ItemAdded(quantity, itemID, kitName)
    return self:Format(self.Templates.ITEM_ADDED, 
        self.Colors.SUCCESS, quantity, itemID, kitName, self.Colors.RESET)
end

function Messages:ItemRemoved(itemID, kitName)
    return self:Format(self.Templates.ITEM_REMOVED, 
        self.Colors.SUCCESS, itemID, kitName, self.Colors.RESET)
end

function Messages:KitCreated(kitName)
    return self:Format(self.Templates.KIT_CREATED, 
        self.Colors.SUCCESS, kitName, self.Colors.RESET)
end

function Messages:KitDeleted(kitName)
    return self:Format(self.Templates.KIT_DELETED, 
        self.Colors.SUCCESS, kitName, self.Colors.RESET)
end

function Messages:ConfigReloaded()
    return self:Format(self.Templates.CONFIG_RELOADED, 
        self.Colors.SUCCESS, self.Colors.RESET)
end

-- Error messages
function Messages:NoPermission()
    return self:Format(self.Templates.NO_PERMISSION, 
        self.Colors.ERROR, self.Colors.RESET)
end

function Messages:PlayerNotFound(playerName)
    return self:Format(self.Templates.PLAYER_NOT_FOUND, 
        self.Colors.ERROR, playerName, self.Colors.RESET)
end

function Messages:KitNotFound(kitName)
    return self:Format(self.Templates.KIT_NOT_FOUND, 
        self.Colors.ERROR, kitName, self.Colors.RESET)
end

function Messages:KitExists(kitName)
    return self:Format(self.Templates.KIT_EXISTS, 
        self.Colors.ERROR, kitName, self.Colors.RESET)
end

function Messages:ItemNotFound(itemID, kitName)
    return self:Format(self.Templates.ITEM_NOT_FOUND, 
        self.Colors.ERROR, itemID, kitName, self.Colors.RESET)
end

function Messages:InvalidQuantity()
    return self:Format(self.Templates.INVALID_QUANTITY, 
        self.Colors.ERROR, self.Colors.RESET)
end

function Messages:InvalidSyntax(correctSyntax)
    return self:Format(self.Templates.INVALID_SYNTAX, 
        self.Colors.ERROR, correctSyntax, self.Colors.RESET)
end

function Messages:InventoryFull()
    return self:Format(self.Templates.INVENTORY_FULL, 
        self.Colors.ERROR, self.Colors.RESET)
end

-- Warning messages
function Messages:KitCooldown(kitName, timeRemaining)
    return self:Format(self.Templates.KIT_COOLDOWN, 
        self.Colors.WARNING, kitName, timeRemaining, self.Colors.RESET)
end

function Messages:LevelRequired(kitName, requiredLevel, playerLevel)
    return self:Format(self.Templates.LEVEL_REQUIRED, 
        self.Colors.WARNING, kitName, requiredLevel, playerLevel, self.Colors.RESET)
end

function Messages:MaxClaims(kitName)
    return self:Format(self.Templates.MAX_CLAIMS, 
        self.Colors.WARNING, kitName, self.Colors.RESET)
end

-- Info messages
function Messages:KitInfo(kit, claims, maxClaims)
    return self:Format(self.Templates.KIT_INFO, 
        self.Colors.INFO, kit.name, kit.level_required, 
        self:FormatTime(kit.cooldown), claims, 
        self:FormatMaxClaims(maxClaims), self.Colors.RESET)
end

function Messages:PlayerStats(playerName, level, totalClaims)
    return self:Format(self.Templates.PLAYER_STATS, 
        self.Colors.INFO, playerName, level, totalClaims, self.Colors.RESET)
end

function Messages:AvailableKits(kitList)
    return self:Format(self.Templates.AVAILABLE_KITS, 
        self.Colors.INFO, kitList, self.Colors.RESET)
end

-- Help messages
function Messages:GetHelpMessage(isAdmin)
    local lines = {
        self:Format(self.Templates.HELP_HEADER, self.Colors.ADMIN, self.Colors.RESET),
        self:Format(self.Templates.HELP_PLAYER, self.Colors.INFO, self.Colors.RESET),
        self:Format(self.Templates.HELP_LIST, self.Colors.INFO, self.Colors.RESET),
        self:Format(self.Templates.HELP_INFO, self.Colors.INFO, self.Colors.RESET)
    }
    
    if isAdmin then
        table.insert(lines, self:Format(self.Templates.HELP_ADMIN_HEADER, self.Colors.ADMIN, self.Colors.RESET))
        table.insert(lines, self:Format(self.Templates.HELP_ADDITEM, self.Colors.INFO, self.Colors.RESET))
        table.insert(lines, self:Format(self.Templates.HELP_REMOVEITEM, self.Colors.INFO, self.Colors.RESET))
        table.insert(lines, self:Format(self.Templates.HELP_KITPREVIEW, self.Colors.INFO, self.Colors.RESET))
        table.insert(lines, self:Format(self.Templates.HELP_CREATEKIT, self.Colors.INFO, self.Colors.RESET))
        table.insert(lines, self:Format(self.Templates.HELP_DELETEKIT, self.Colors.INFO, self.Colors.RESET))
        table.insert(lines, self:Format(self.Templates.HELP_GIVEKIT, self.Colors.INFO, self.Colors.RESET))
        table.insert(lines, self:Format(self.Templates.HELP_RELOAD, self.Colors.INFO, self.Colors.RESET))
        table.insert(lines, self:Format(self.Templates.HELP_SETLEVEL, self.Colors.INFO, self.Colors.RESET))
    end
    
    return table.concat(lines, "\n")
end

return Messages
