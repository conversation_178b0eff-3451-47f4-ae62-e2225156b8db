local Json = require("libs/json")
local ModDirectory = debug.getinfo(1, "S").source:match([[^@?(.*[\/])[^\/]-$]])

local KitManager = {}

-- File paths
KitManager.ConfigFile = ModDirectory .. "kit_config.json"
KitManager.PlayerDataFile = ModDirectory .. "player_data.json"

-- In-memory storage
KitManager.Kits = {}
KitManager.PlayerData = {}
KitManager.Settings = {}

-- Utility functions
local function EncodeJSON(data)
    return Json.encode(data)
end

local function DecodeJSON(content)
    local ok, data = pcall(Json.decode, content)
    return ok and data or nil
end

local function GetCurrentTime()
    return os.time()
end

-- File operations
function KitManager:SaveKitConfig()
    local config = {
        settings = self.Settings,
        kits = self.Kits
    }
    
    local file = io.open(self.ConfigFile, "w")
    if file then
        file:write(EncodeJSON(config))
        file:close()
        return true
    end
    return false
end

function KitManager:LoadKitConfig()
    -- First try to load from JSON file
    local file = io.open(self.ConfigFile, "r")
    if file then
        local content = file:read("*a")
        file:close()
        
        local config = DecodeJSON(content)
        if config then
            self.Settings = config.settings or {}
            self.Kits = config.kits or {}
            return true
        end
    end
    
    -- Fallback to kit.lua if JSON doesn't exist
    local ok, kitConfig = pcall(require, "kit")
    if ok and kitConfig then
        self.Settings = kitConfig.settings or {}
        self.Kits = kitConfig.kits or {}
        -- Save to JSON for future use
        self:SaveKitConfig()
        return true
    end
    
    return false
end

function KitManager:SavePlayerData()
    local file = io.open(self.PlayerDataFile, "w")
    if file then
        file:write(EncodeJSON(self.PlayerData))
        file:close()
        return true
    end
    return false
end

function KitManager:LoadPlayerData()
    local file = io.open(self.PlayerDataFile, "r")
    if not file then
        -- Create empty file
        local newFile = io.open(self.PlayerDataFile, "w")
        if newFile then
            newFile:write("{}")
            newFile:close()
        end
        self.PlayerData = {}
        return true
    end
    
    local content = file:read("*a")
    file:close()
    
    local data = DecodeJSON(content)
    if data then
        self.PlayerData = data
        return true
    end
    
    self.PlayerData = {}
    return false
end

-- Player data management
function KitManager:EnsurePlayerExists(playerName)
    if not self.PlayerData[playerName] then
        self.PlayerData[playerName] = {
            level = 1,
            kit_claims = {},
            last_kit_times = {},
            total_claims = 0
        }
        self:SavePlayerData()
    end
end

function KitManager:GetPlayerData(playerName)
    self:EnsurePlayerExists(playerName)
    return self.PlayerData[playerName]
end

function KitManager:UpdatePlayerLevel(playerName, level)
    local playerData = self:GetPlayerData(playerName)
    playerData.level = level
    self:SavePlayerData()
end

-- Kit management functions
function KitManager:KitExists(kitName)
    return self.Kits[kitName] ~= nil
end

function KitManager:GetKit(kitName)
    return self.Kits[kitName]
end

function KitManager:GetAllKits()
    return self.Kits
end

function KitManager:CreateKit(kitName, kitData)
    if self:KitExists(kitName) then
        return false, "Kit already exists"
    end
    
    -- Validate kit data
    local defaultKit = {
        name = kitData.name or kitName,
        description = kitData.description or "No description",
        level_required = kitData.level_required or 1,
        cooldown = kitData.cooldown or self.Settings.default_cooldown or 3600,
        max_claims = kitData.max_claims or -1,
        auto_grant = kitData.auto_grant or false,
        items = kitData.items or {}
    }
    
    self.Kits[kitName] = defaultKit
    self:SaveKitConfig()
    return true, "Kit created successfully"
end

function KitManager:DeleteKit(kitName)
    if not self:KitExists(kitName) then
        return false, "Kit does not exist"
    end
    
    self.Kits[kitName] = nil
    self:SaveKitConfig()
    return true, "Kit deleted successfully"
end

function KitManager:AddItemToKit(kitName, itemID, quantity)
    if not self:KitExists(kitName) then
        return false, "Kit does not exist"
    end
    
    self.Kits[kitName].items[itemID] = quantity
    self:SaveKitConfig()
    return true, "Item added to kit"
end

function KitManager:RemoveItemFromKit(kitName, itemID)
    if not self:KitExists(kitName) then
        return false, "Kit does not exist"
    end
    
    if not self.Kits[kitName].items[itemID] then
        return false, "Item not found in kit"
    end
    
    self.Kits[kitName].items[itemID] = nil
    self:SaveKitConfig()
    return true, "Item removed from kit"
end

-- Kit claiming logic
function KitManager:CanClaimKit(playerName, kitName)
    local kit = self:GetKit(kitName)
    if not kit then
        return false, "Kit does not exist"
    end
    
    local playerData = self:GetPlayerData(playerName)
    
    -- Check level requirement
    if kit.level_required > playerData.level then
        return false, string.format("Requires level %d (you are level %d)", kit.level_required, playerData.level)
    end
    
    -- Check max claims
    local claims = playerData.kit_claims[kitName] or 0
    if kit.max_claims > 0 and claims >= kit.max_claims then
        return false, "Maximum claims reached for this kit"
    end
    
    -- Check cooldown
    local lastClaim = playerData.last_kit_times[kitName] or 0
    local currentTime = GetCurrentTime()
    local timeSinceLastClaim = currentTime - lastClaim
    
    if timeSinceLastClaim < kit.cooldown then
        local remainingTime = kit.cooldown - timeSinceLastClaim
        local hours = math.floor(remainingTime / 3600)
        local minutes = math.floor((remainingTime % 3600) / 60)
        local seconds = remainingTime % 60
        
        local timeStr = ""
        if hours > 0 then timeStr = timeStr .. hours .. "h " end
        if minutes > 0 then timeStr = timeStr .. minutes .. "m " end
        if seconds > 0 then timeStr = timeStr .. seconds .. "s" end
        
        return false, string.format("Kit on cooldown. Time remaining: %s", timeStr)
    end
    
    return true, "Can claim kit"
end

function KitManager:ClaimKit(playerName, kitName)
    local canClaim, message = self:CanClaimKit(playerName, kitName)
    if not canClaim then
        return false, message
    end
    
    local kit = self:GetKit(kitName)
    local playerData = self:GetPlayerData(playerName)
    
    -- Update claim data
    playerData.kit_claims[kitName] = (playerData.kit_claims[kitName] or 0) + 1
    playerData.last_kit_times[kitName] = GetCurrentTime()
    playerData.total_claims = playerData.total_claims + 1
    
    self:SavePlayerData()
    
    return true, kit.items
end

-- Initialize the manager
function KitManager:Initialize()
    self:LoadKitConfig()
    self:LoadPlayerData()
    print("[KitManager] Initialized successfully")
end

-- Reload configuration
function KitManager:Reload()
    self:LoadKitConfig()
    print("[KitManager] Configuration reloaded")
    return true, "Kit configuration reloaded successfully"
end

return KitManager
