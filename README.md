# Advanced Kit Management System for Palworld

A comprehensive kit management system that allows administrators to create, manage, and distribute item kits to players with advanced features like cooldowns, level requirements, and claim tracking.

## Features

### 🎮 Player Features
- **Multiple Kit Types**: Access different kits based on your level and needs
- **Automatic Starter Kit**: New players automatically receive a starter kit on first join
- **Level-Based Access**: Kits unlock as you progress in the game
- **Cooldown System**: Prevents kit spam with configurable cooldowns
- **Claim Tracking**: Track how many times you've claimed each kit

### 🛠️ Admin Features
- **Dynamic Kit Creation**: Create new kits instantly without server restart
- **Item Management**: Add/remove items from existing kits
- **Kit Preview**: View kit contents before giving to players
- **Player Management**: Give kits to specific players
- **Live Configuration**: Reload configurations without restart
- **Comprehensive Logging**: Track all kit operations

## Available Commands

### Player Commands
- `/kit <kitname>` - Claim a specific kit
- `/kitlist` - List all available kits
- `/kitinfo <kitname>` - View detailed information about a kit
- `/kithelp` - Display help information

### Admin Commands
- `/additem <kitname> <itemid> <quantity>` - Add item to a kit
- `/removeitem <kitname> <itemid>` - Remove item from a kit
- `/kitpreview <kitname>` - Preview kit contents
- `/createkit <kitname>` - Create a new empty kit
- `/givekit <player> <kitname>` - Give kit to a specific player
- `/kitreload` - Reload kit configuration from files

## Default Kits

### Starter Kit (Level 1)
**Auto-granted on first join**
- Fur Helmet
- Cloth Armor
- Basic Shield
- Wooden Bat
- Torch
- Stone Axe
- Stone Pickaxe
- 10x Pal Spheres
- 15x Bread

### PvP Kit (Level 10)
**Cooldown: 2 hours**
- Metal Armor
- Metal Helmet
- Assault Rifle
- 100x Rifle Ammo
- 5x Hand Grenades
- 10x Medical Supplies
- Advanced Shield

### Builder Kit (Level 5)
**Cooldown: 1 hour**
- 500x Wood
- 300x Stone
- 200x Fiber
- 100x Metal Ingots
- 50x Copper Ingots
- 50x Iron Ingots
- 100x Cement

### Farming Kit (Level 8)
**Cooldown: 30 minutes**
- 20x Tomato Seeds
- 20x Lettuce Seeds
- 20x Wheat Seeds
- 20x Berry Seeds
- Watering Pot
- 50x Fertilizer
- Hoe

### Mining Kit (Level 15)
**Cooldown: 1.5 hours**
- Advanced Pickaxe
- Metal Pickaxe
- 10x Dynamite
- 5x Lanterns
- 20x Rope
- 2x Metal Chests

## Configuration

The system uses two main configuration files:

### kit_config.json
Stores all kit definitions and settings. Automatically created from `kit.lua` on first run.

### player_data.json
Stores player-specific data including:
- Player levels
- Kit claim history
- Last claim timestamps
- Total claims

## Installation

1. Place all files in your UE4SS mod directory
2. Ensure the mod is enabled in your UE4SS configuration
3. Restart your Palworld server
4. The system will automatically initialize and create necessary files

## Kit Configuration Structure

```json
{
  "settings": {
    "auto_save": true,
    "save_interval": 300,
    "max_kits_per_player": 50,
    "default_cooldown": 3600,
    "level_check_enabled": true
  },
  "kits": {
    "kitname": {
      "name": "Display Name",
      "description": "Kit description",
      "level_required": 1,
      "cooldown": 3600,
      "max_claims": -1,
      "auto_grant": false,
      "items": {
        "ItemID": quantity
      }
    }
  }
}
```

## Kit Properties

- **name**: Display name for the kit
- **description**: Brief description of the kit's purpose
- **level_required**: Minimum player level to claim the kit
- **cooldown**: Time in seconds between claims (0 = no cooldown)
- **max_claims**: Maximum times a player can claim this kit (-1 = unlimited)
- **auto_grant**: Whether to automatically give this kit to new players
- **items**: Object containing ItemID -> quantity mappings

## User-Friendly Messages

The system provides clear, color-coded feedback for all operations:
- 🟢 **Green**: Success messages
- 🔴 **Red**: Error messages  
- 🟡 **Yellow**: Warning messages
- 🔵 **Blue**: Information messages
- 🟣 **Purple**: Admin messages

## Live Sync Features

- **Auto-save**: Player data is automatically saved every 5 minutes
- **Instant Updates**: Configuration changes take effect immediately
- **Reload Command**: Admins can reload configurations without restart
- **Real-time Tracking**: All kit claims and cooldowns are tracked in real-time

## Troubleshooting

### Common Issues

1. **Kit not appearing**: Check if player meets level requirements
2. **Cooldown errors**: Verify cooldown has expired using `/kitinfo`
3. **Permission denied**: Ensure player has admin privileges for admin commands
4. **Items not received**: Check if player inventory has space

### Debug Information

The system logs important events to the console:
- Kit claims and failures
- Configuration reloads
- Player data updates
- Error conditions

## Support

For issues or feature requests, check the mod's documentation or contact the server administrator.

## Version History

- **v2.0**: Complete rewrite with advanced features
- **v1.0**: Basic starter kit functionality
