return {
    enable = true,
    -- Global settings
    settings = {
        auto_save = true,
        save_interval = 300, -- seconds
        max_kits_per_player = 50,
        default_cooldown = 3600, -- 1 hour in seconds
        level_check_enabled = true
    },

    -- Kit definitions
    kits = {
        -- Starter kit - given automatically on first join
        starterkit = {
            name = "Starter Kit",
            description = "Basic survival items for new players",
            level_required = 1,
            cooldown = 0, -- No cooldown for starter kit
            max_claims = 1, -- Can only be claimed once
            auto_grant = true, -- Automatically given on first join
            items = {
                ["FurHelmet"] = 1,
                ["ClothArmor"] = 1,
                ["Shield_01"] = 1,
                ["Bat"] = 1,
                ["Torch"] = 1,
                ["Axe_Tier_00"] = 1,
                ["Pickaxe_Tier_00"] = 1,
                ["PalSphere"] = 10,
                ["Pan"] = 15,
            }
        },

        -- PvP kit for combat
        pvpkit = {
            name = "PvP Kit",
            description = "Combat gear for player vs player battles",
            level_required = 2,
            cooldown = 7200, -- 2 hours
            max_claims = -1, -- Unlimited claims (with cooldown)
            auto_grant = false,
            items = {
                ["MetalArmor"] = 1,
                ["MetalHelm"] = 1,
                ["AssaultRifle_Default1"] = 1,
                ["Ammo_AssaultRifle_Default"] = 100,
                ["HandGrenade"] = 5,
                ["MedicalSupplies"] = 10,
                ["Shield_02"] = 1
            }
        },

        -- Building kit for construction
        buildkit = {
            name = "Builder Kit",
            description = "Materials and tools for construction",
            level_required = 4,
            cooldown = 3600, -- 1 hour
            max_claims = -1,
            auto_grant = false,
            items = {
                ["Wood"] = 500,
                ["Stone"] = 300,
                ["Fiber"] = 200,
                ["Ingot"] = 100,
                ["CopperIngot"] = 50,
                ["IronIngot"] = 50,
                ["Cement"] = 100
            }
        },

        -- Farming kit for agriculture
        farmkit = {
            name = "Farming Kit",
            description = "Seeds and tools for agriculture",
            level_required = 8,
            cooldown = 1800, -- 30 minutes
            max_claims = -1,
            auto_grant = false,
            items = {
                ["TomatoSeed"] = 20,
                ["LettuceSeeds"] = 20,
                ["WheatSeeds"] = 20,
                ["BerrySeeds"] = 20,
                ["WateringPot"] = 1,
                ["Fertilizer"] = 50,
                ["Hoe"] = 1
            }
        },

        -- Mining kit for resource gathering
        miningkit = {
            name = "Mining Kit",
            description = "Advanced tools for mining operations",
            level_required = 15,
            cooldown = 5400, -- 1.5 hours
            max_claims = -1,
            auto_grant = false,
            items = {
                ["Pickaxe_Tier_02"] = 1,
                ["Pickaxe_Tier_01"] = 1,
                ["Dynamite"] = 10,
                ["Lantern"] = 5,
                ["Rope"] = 20,
                ["MetalChest"] = 2
            }
        }
    }
}
