return {
    enable = true,
    -- Global settings
    settings = {
        auto_save = true,
        save_interval = 300, -- seconds
        max_kits_per_player = 50,
        default_cooldown = 3600, -- 1 hour in seconds
        level_check_enabled = true
    },

    -- Kit definitions - Minimal showcase setup
    kits = {
        -- Starter kit - given automatically on first join
        starterkit = {
            name = "Starter Kit",
            description = "Basic survival items for new players",
            level_required = 1,
            cooldown = 0, -- No cooldown for starter kit
            max_claims = 1, -- Can only be claimed once
            auto_grant = true, -- Automatically given on first join
            items = {
                ["FurHelmet"] = 1,
                ["ClothArmor"] = 1,
                ["Shield_01"] = 1,
                ["Bat"] = 1,
                ["Torch"] = 1,
                ["Axe_Tier_00"] = 1,
                ["Pickaxe_Tier_00"] = 1,
                ["PalSphere"] = 10,
                ["Pan"] = 15,
            }
        },

        -- PvP kit for combat - Showcase example
        pvpkit = {
            name = "PvP Kit",
            description = "Combat gear for player vs player battles",
            level_required = 10,
            cooldown = 7200, -- 2 hours
            max_claims = -1, -- Unlimited claims (with cooldown)
            auto_grant = false, -- Manual claim only
            items = {
                ["MetalArmor"] = 1,
                ["MetalHelm"] = 1,
                ["AssaultRifle_Default1"] = 1,
                ["Ammo_AssaultRifle_Default"] = 100,
                ["HandGrenade"] = 5,
                ["MedicalSupplies"] = 10,
                ["Shield_02"] = 1
            }
        }

        -- Add more kits here as needed
        -- Use !createkit command to add new kits in-game
        -- Or edit this file and use !kitreload to refresh
    }
}
