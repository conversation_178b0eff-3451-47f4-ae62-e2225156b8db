-- Test script for Kit Management System
-- This script can be used to verify the system is working correctly

local KitManager = require("Scripts/kit_manager")
local Messages = require("Scripts/messages")

print("=== Kit Management System Test ===")

-- Initialize the system
KitManager:Initialize()

-- Test 1: Check if kits are loaded
print("\n1. Testing kit loading...")
local kits = KitManager:GetAllKits()
local kitCount = 0
for kitName, kit in pairs(kits) do
    kitCount = kitCount + 1
    print(string.format("   - %s: %s (Level %d)", kitName, kit.name, kit.level_required))
end
print(string.format("   Total kits loaded: %d", kitCount))

-- Test 2: Test player data management
print("\n2. Testing player data management...")
local testPlayer = "TestPlayer"
KitManager:EnsurePlayerExists(testPlayer)
local playerData = KitManager:GetPlayerData(testPlayer)
print(string.format("   - Player %s created with level %d", testPlayer, playerData.level))

-- Test 3: Test kit claiming logic
print("\n3. Testing kit claiming logic...")

-- Test starter kit claim
local canClaim, message = KitManager:CanClaimKit(testPlayer, "starterkit")
print(string.format("   - Can claim starter kit: %s (%s)", tostring(canClaim), message))

-- Test level requirement
local canClaimPvP, pvpMessage = KitManager:CanClaimKit(testPlayer, "pvpkit")
print(string.format("   - Can claim PvP kit: %s (%s)", tostring(canClaimPvP), pvpMessage))

-- Update player level and test again
KitManager:UpdatePlayerLevel(testPlayer, 15)
local canClaimPvP2, pvpMessage2 = KitManager:CanClaimKit(testPlayer, "pvpkit")
print(string.format("   - Can claim PvP kit after level up: %s (%s)", tostring(canClaimPvP2), pvpMessage2))

-- Test 4: Test kit creation
print("\n4. Testing kit creation...")
local testKitData = {
    name = "Test Kit",
    description = "A test kit for verification",
    level_required = 5,
    cooldown = 1800,
    max_claims = 3,
    auto_grant = false,
    items = {
        ["TestItem"] = 10,
        ["AnotherItem"] = 5
    }
}

local createSuccess, createMessage = KitManager:CreateKit("testkit", testKitData)
print(string.format("   - Kit creation: %s (%s)", tostring(createSuccess), createMessage))

-- Test 5: Test item management
print("\n5. Testing item management...")
local addSuccess, addMessage = KitManager:AddItemToKit("testkit", "NewItem", 20)
print(string.format("   - Add item: %s (%s)", tostring(addSuccess), addMessage))

local removeSuccess, removeMessage = KitManager:RemoveItemFromKit("testkit", "TestItem")
print(string.format("   - Remove item: %s (%s)", tostring(removeSuccess), removeMessage))

-- Test 6: Test message formatting
print("\n6. Testing message system...")
local successMsg = Messages:KitClaimed("TestPlayer", "starterkit")
print(string.format("   - Success message: %s", successMsg))

local errorMsg = Messages:KitNotFound("nonexistent")
print(string.format("   - Error message: %s", errorMsg))

local infoMsg = Messages:KitInfo(KitManager:GetKit("starterkit"), 0, 1)
print(string.format("   - Info message: %s", infoMsg))

-- Test 7: Test configuration save/load
print("\n7. Testing configuration persistence...")
local saveSuccess = KitManager:SaveKitConfig()
print(string.format("   - Save config: %s", tostring(saveSuccess)))

local savePlayerSuccess = KitManager:SavePlayerData()
print(string.format("   - Save player data: %s", tostring(savePlayerSuccess)))

-- Test 8: Test reload functionality
print("\n8. Testing reload functionality...")
local reloadSuccess, reloadMessage = KitManager:Reload()
print(string.format("   - Reload: %s (%s)", tostring(reloadSuccess), reloadMessage))

-- Test 9: Verify kit exists after reload
print("\n9. Verifying data persistence...")
local testKitExists = KitManager:KitExists("testkit")
print(string.format("   - Test kit exists after reload: %s", tostring(testKitExists)))

if testKitExists then
    local testKit = KitManager:GetKit("testkit")
    print(string.format("   - Test kit name: %s", testKit.name))
    print(string.format("   - Test kit level requirement: %d", testKit.level_required))
    
    local itemCount = 0
    for itemID, quantity in pairs(testKit.items) do
        itemCount = itemCount + 1
        print(string.format("   - Item: %s x%d", itemID, quantity))
    end
    print(string.format("   - Total items in test kit: %d", itemCount))
end

-- Test 10: Clean up test data
print("\n10. Cleaning up test data...")
local deleteSuccess, deleteMessage = KitManager:DeleteKit("testkit")
print(string.format("   - Delete test kit: %s (%s)", tostring(deleteSuccess), deleteMessage))

print("\n=== Test Complete ===")
print("If all tests show 'true' for success operations, the system is working correctly!")
print("You can now use the kit management commands in-game.")

-- Display available commands
print("\n=== Available Commands ===")
print("Player Commands:")
print("  /kit <kitname> - Claim a kit")
print("  /kitlist - List available kits")
print("  /kitinfo <kitname> - View kit information")
print("  /kithelp - Show help")
print("\nAdmin Commands:")
print("  /additem <kitname> <itemid> <quantity> - Add item to kit")
print("  /removeitem <kitname> <itemid> - Remove item from kit")
print("  /kitpreview <kitname> - Preview kit contents")
print("  /createkit <kitname> - Create new kit")
print("  /givekit <player> <kitname> - Give kit to player")
print("  /kitreload - Reload configuration")
