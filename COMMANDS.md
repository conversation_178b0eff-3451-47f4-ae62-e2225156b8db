# Kit Management Commands Reference

## 🎮 Player Commands (Available to Everyone)

### `/kit <kitname>`
**Description:** Claim a specific kit  
**Example:** `/kit pvpkit`  
**Requirements:** Must meet level requirement and cooldown  

### `/kitlist`
**Description:** List all available kits  
**Example:** `/kitlist`  
**Shows:** Kit names that you can claim  

### `/kitinfo <kitname>`
**Description:** View detailed information about a kit  
**Example:** `/kitinfo buildkit`  
**Shows:** Level requirement, cooldown, items, your claim status  

### `/kithelp`
**Description:** Display help information  
**Example:** `/kithelp`  
**Shows:** All available commands based on your permissions  

---

## 🛠️ Admin Commands (Admin Only)

### `/additem <kitname> <itemid> <quantity>`
**Description:** Add an item to an existing kit  
**Example:** `/additem pvpkit Sword_Tier_02 1`  
**Note:** Creates the item entry or updates quantity if exists  

### `/removeitem <kitname> <itemid>`
**Description:** Remove an item from a kit  
**Example:** `/removeitem pvpkit OldWeapon`  
**Note:** Completely removes the item from the kit  

### `/kitpreview <kitname>`
**Description:** Preview all items in a kit with quantities  
**Example:** `/kitpreview starterkit`  
**Shows:** Complete list of items and their quantities  

### `/createkit <kitname>`
**Description:** Create a new empty kit  
**Example:** `/createkit customkit`  
**Note:** Creates kit with default settings (level 1, 1 hour cooldown)  

### `/givekit <player> <kitname>`
**Description:** Give a kit directly to a specific player  
**Example:** `/givekit PlayerName pvpkit`  
**Note:** Bypasses level and cooldown requirements  

### `/kitreload`
**Description:** Reload kit configuration from files  
**Example:** `/kitreload`  
**Note:** Refreshes all kit data without server restart  

---

## 📋 Available Kits

| Kit Name | Level Required | Cooldown | Description |
|----------|----------------|----------|-------------|
| `starterkit` | 1 | None | Basic survival items (auto-granted) |
| `pvpkit` | 2 | 2 hours | Combat gear for PvP |
| `buildkit` | 4 | 1 hour | Construction materials |
| `farmkit` | 8 | 30 minutes | Agricultural supplies |
| `miningkit` | 15 | 1.5 hours | Advanced mining tools |

---

## 💡 Tips

- **Command Prefixes:** Both `/` and `!` work as command prefixes
- **Case Insensitive:** Commands work in any case (e.g., `/KIT`, `/kit`, `/Kit`)
- **Auto-Complete:** Kit names must be typed exactly as shown
- **Cooldowns:** Use `/kitinfo <kitname>` to check remaining cooldown time
- **Admin Status:** Admin commands require server admin privileges
- **Starter Kit:** Automatically given to new players on first join

---

## 🔧 Common Item IDs for Admins

### Weapons
- `Bat` - Wooden bat
- `Sword_Tier_01` - Metal sword
- `AssaultRifle_Default1` - Assault rifle
- `Bow_Default` - Basic bow

### Armor
- `ClothArmor` - Cloth armor
- `MetalArmor` - Metal armor
- `FurHelmet` - Fur helmet
- `MetalHelm` - Metal helmet

### Tools
- `Axe_Tier_00` - Stone axe
- `Pickaxe_Tier_00` - Stone pickaxe
- `Pickaxe_Tier_01` - Metal pickaxe
- `Pickaxe_Tier_02` - Advanced pickaxe

### Resources
- `Wood` - Wood
- `Stone` - Stone
- `Fiber` - Fiber
- `Ingot` - Metal ingot
- `CopperIngot` - Copper ingot
- `IronIngot` - Iron ingot

### Consumables
- `Pan` - Bread
- `MedicalSupplies` - Medical supplies
- `PalSphere` - Pal sphere
- `Torch` - Torch

---

## 🚨 Error Messages

- **"You do not have permission"** - Command requires admin privileges
- **"Kit does not exist"** - Check kit name spelling with `/kitlist`
- **"Requires level X"** - Player level too low for this kit
- **"Kit on cooldown"** - Must wait before claiming again
- **"Maximum claims reached"** - Kit has limited uses
- **"Player not found"** - Target player not online (for `/givekit`)
- **"Cannot access inventory"** - Player inventory issue

---

## 📞 Support

If commands are not working:
1. Check if you're using the correct syntax
2. Verify you have the required permissions
3. Ensure the kit name exists with `/kitlist`
4. Check server console for error messages
5. Try `/kitreload` if you're an admin
