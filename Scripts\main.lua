-- Advanced Kit Management System for Palworld
-- Supports multiple kits, cooldowns, level requirements, and admin management

local Json = require("libs/json")
local FGuid = require("libs/fguid")
local KitManager = require("kit_manager")
local Messages = require("messages")

-- Initialize the kit manager
KitManager:Initialize()

-- Utility functions
local function GetPlayerName(ps)
    if ps and ps:IsValid() and ps.PlayerNamePrivate then
        local ok, name = pcall(function() return ps.PlayerNamePrivate:ToString() end)
        if ok then
            return name
        end
    end
    return "Unknown"
end

local function SendMessageToPlayer(playerController, message)
    local palUtility = StaticFindObject("/Script/Pal.Default__PalUtility")
    local world = FindFirstOf("World")
    local playerState = playerController.PlayerState
    local playerUid = playerState and playerState.PlayerUId
    
    if playerUid then
        local guid = FGuid.translate(playerUid)
        ExecuteWithDelay(100, function()
            palUtility:SendSystemToPlayerChat(world, message, guid)
        end)
    end
end

local function FindPlayerByName(targetName)
    local palUtility = StaticFindObject("/Script/Pal.Default__PalUtility")
    local world = FindFirstOf("World")
    local playerList = palUtility:GetPlayerListDisplayMessages(world)
    
    if playerList then
        for i = 1, #playerList do
            local info = playerList[i]:get():ToString()
            local name = info:match("^(.-),")
            if name == targetName then
                local playerChar = palUtility:GetPlayerCharacterByPlayerIndex(world, i - 1)
                if playerChar and playerChar:IsValid() then
                    return playerChar:GetPalPlayerController()
                end
            end
        end
    end
    return nil
end

local function GiveItemsToPlayer(playerState, items)
    local inv = playerState:GetInventoryData()
    if not inv or not inv:IsValid() then
        return false, "Cannot access player inventory"
    end
    
    for itemID, quantity in pairs(items) do
        inv:AddItem_ServerInternal(FName(itemID), quantity, false, 0)
    end
    
    return true, "Items given successfully"
end

local function UpdatePlayerLevel(playerName, playerState)
    -- Try to get player level from character stats
    -- This is a placeholder - you may need to adjust based on how Palworld exposes level data
    local level = 1
    
    -- Update in kit manager
    KitManager:UpdatePlayerLevel(playerName, level)
end

-- Auto-grant all kits on first join
local function GiveAllKitsIfNeeded(playerName, playerState)
    local allKits = KitManager:GetAllKits()
    
    for kitName, kit in pairs(allKits) do
        if kit.auto_grant then
            local playerData = KitManager:GetPlayerData(playerName)
            local claims = playerData.kit_claims[kitName] or 0
            
            if claims == 0 then
                local success, items = KitManager:ClaimKit(playerName, kitName)
                if success then
                    local giveSuccess, giveMessage = GiveItemsToPlayer(playerState, items)
                    if giveSuccess then
                        print(string.format("[KitManager] Gave %s kit to %s", kitName, playerName))
                    end
                end
            end
        end
    end
end

-- Parse command arguments
local function ParseArgs(text)
    local args = {}
    for arg in text:gmatch("%S+") do
        table.insert(args, arg)
    end
    return args
end

-- Command handlers
local CommandHandlers = {}

-- Player commands (no admin required)
function CommandHandlers:HandleKit(playerController, args)
    local kitName = args[1]
    if not kitName then
        SendMessageToPlayer(playerController, Messages:InvalidSyntax("!kit <kitname>"))
        return
    end
    
    local playerState = playerController.PlayerState
    local playerName = GetPlayerName(playerState)
    
    -- Update player level before checking kit eligibility
    UpdatePlayerLevel(playerName, playerState)
    
    local success, result = KitManager:ClaimKit(playerName, kitName)
    if success then
        local giveSuccess, giveMessage = GiveItemsToPlayer(playerState, result)
        if giveSuccess then
            SendMessageToPlayer(playerController, Messages:KitClaimed(playerName, kitName))
        else
            SendMessageToPlayer(playerController, Messages:InventoryFull())
        end
    else
        SendMessageToPlayer(playerController, result) -- result contains error message
    end
end

function CommandHandlers:HandleKitList(playerController, args)
    local kits = KitManager:GetAllKits()
    local kitNames = {}
    
    for kitName, kit in pairs(kits) do
        table.insert(kitNames, kit.name)
    end
    
    if #kitNames > 0 then
        local kitList = table.concat(kitNames, ", ")
        SendMessageToPlayer(playerController, Messages:AvailableKits(kitList))
    else
        SendMessageToPlayer(playerController, Messages:AvailableKits("None"))
    end
end

function CommandHandlers:HandleKitInfo(playerController, args)
    local kitName = args[1]
    if not kitName then
        SendMessageToPlayer(playerController, Messages:InvalidSyntax("!kitinfo <kitname>"))
        return
    end
    
    local kit = KitManager:GetKit(kitName)
    if not kit then
        SendMessageToPlayer(playerController, Messages:KitNotFound(kitName))
        return
    end
    
    local playerState = playerController.PlayerState
    local playerName = GetPlayerName(playerState)
    local playerData = KitManager:GetPlayerData(playerName)
    local claims = playerData.kit_claims[kitName] or 0
    
    SendMessageToPlayer(playerController, Messages:KitInfo(kit, claims, kit.max_claims))
end

function CommandHandlers:HandleHelp(playerController, args)
    local isAdmin = playerController.bAdmin
    local helpMessage = Messages:GetHelpMessage(isAdmin)
    SendMessageToPlayer(playerController, helpMessage)
end

-- Admin commands
function CommandHandlers:HandleAddItem(playerController, args)
    if not playerController.bAdmin then
        SendMessageToPlayer(playerController, Messages:NoPermission())
        return
    end
    
    local kitName, itemID, quantityStr = args[1], args[2], args[3]
    if not kitName or not itemID or not quantityStr then
        SendMessageToPlayer(playerController, Messages:InvalidSyntax("!additem <kitname> <itemid> <quantity>"))
        return
    end
    
    local quantity = tonumber(quantityStr)
    if not quantity or quantity <= 0 then
        SendMessageToPlayer(playerController, Messages:InvalidQuantity())
        return
    end
    
    local success, message = KitManager:AddItemToKit(kitName, itemID, quantity)
    if success then
        SendMessageToPlayer(playerController, Messages:ItemAdded(quantity, itemID, kitName))
    else
        SendMessageToPlayer(playerController, message)
    end
end

function CommandHandlers:HandleRemoveItem(playerController, args)
    if not playerController.bAdmin then
        SendMessageToPlayer(playerController, Messages:NoPermission())
        return
    end
    
    local kitName, itemID = args[1], args[2]
    if not kitName or not itemID then
        SendMessageToPlayer(playerController, Messages:InvalidSyntax("!removeitem <kitname> <itemid>"))
        return
    end
    
    local success, message = KitManager:RemoveItemFromKit(kitName, itemID)
    if success then
        SendMessageToPlayer(playerController, Messages:ItemRemoved(itemID, kitName))
    else
        SendMessageToPlayer(playerController, message)
    end
end

function CommandHandlers:HandleKitPreview(playerController, args)
    if not playerController.bAdmin then
        SendMessageToPlayer(playerController, Messages:NoPermission())
        return
    end
    
    local kitName = args[1]
    if not kitName then
        SendMessageToPlayer(playerController, Messages:InvalidSyntax("!kitpreview <kitname>"))
        return
    end
    
    local kit = KitManager:GetKit(kitName)
    if not kit then
        SendMessageToPlayer(playerController, Messages:KitNotFound(kitName))
        return
    end
    
    local itemList = {}
    for itemID, quantity in pairs(kit.items) do
        table.insert(itemList, string.format("%dx %s", quantity, itemID))
    end
    
    local message = string.format("§b%s Contents:§r\n%s", kit.name, table.concat(itemList, "\n"))
    SendMessageToPlayer(playerController, message)
end

function CommandHandlers:HandleCreateKit(playerController, args)
    if not playerController.bAdmin then
        SendMessageToPlayer(playerController, Messages:NoPermission())
        return
    end
    
    local kitName = args[1]
    if not kitName then
        SendMessageToPlayer(playerController, Messages:InvalidSyntax("!createkit <kitname>"))
        return
    end
    
    local kitData = {
        name = kitName,
        description = "Custom kit created by admin",
        level_required = 1,
        cooldown = 3600,
        max_claims = -1,
        auto_grant = true,  -- Make all new kits auto-grant
        items = {}
    }
    
    local success, message = KitManager:CreateKit(kitName, kitData)
    if success then
        SendMessageToPlayer(playerController, Messages:KitCreated(kitName))
    else
        SendMessageToPlayer(playerController, message)
    end
end

function CommandHandlers:HandleGiveKit(playerController, args)
    if not playerController.bAdmin then
        SendMessageToPlayer(playerController, Messages:NoPermission())
        return
    end
    
    local targetName, kitName = args[1], args[2]
    if not targetName or not kitName then
        SendMessageToPlayer(playerController, Messages:InvalidSyntax("!givekit <player> <kitname>"))
        return
    end
    
    local targetController = FindPlayerByName(targetName)
    if not targetController then
        SendMessageToPlayer(playerController, Messages:PlayerNotFound(targetName))
        return
    end
    
    local targetState = targetController.PlayerState
    local targetPlayerName = GetPlayerName(targetState)
    
    local kit = KitManager:GetKit(kitName)
    if not kit then
        SendMessageToPlayer(playerController, Messages:KitNotFound(kitName))
        return
    end
    
    local giveSuccess, giveMessage = GiveItemsToPlayer(targetState, kit.items)
    if giveSuccess then
        -- Update player data to record the kit claim
        local playerData = KitManager:GetPlayerData(targetPlayerName)
        playerData.kit_claims[kitName] = (playerData.kit_claims[kitName] or 0) + 1
        playerData.total_claims = playerData.total_claims + 1
        KitManager:SavePlayerData()
        
        SendMessageToPlayer(playerController, Messages:KitGiven(kitName, targetPlayerName))
        SendMessageToPlayer(targetController, Messages:KitClaimed(targetPlayerName, kitName))
    else
        SendMessageToPlayer(playerController, Messages:InventoryFull())
    end
end

function CommandHandlers:HandleKitReload(playerController, args)
    if not playerController.bAdmin then
        SendMessageToPlayer(playerController, Messages:NoPermission())
        return
    end

    local success, message = KitManager:Reload()
    if success then
        SendMessageToPlayer(playerController, Messages:ConfigReloaded())
    else
        SendMessageToPlayer(playerController, message)
    end
end

-- Command routing table - MUST be defined before the hook!
local Commands = {
    -- Player commands (no admin required)
    ["kit"] = CommandHandlers.HandleKit,
    ["kitlist"] = CommandHandlers.HandleKitList,
    ["kitinfo"] = CommandHandlers.HandleKitInfo,
    ["kithelp"] = CommandHandlers.HandleHelp,

    -- Admin commands (admin required)
    ["additem"] = CommandHandlers.HandleAddItem,
    ["removeitem"] = CommandHandlers.HandleRemoveItem,
    ["kitpreview"] = CommandHandlers.HandleKitPreview,
    ["createkit"] = CommandHandlers.HandleCreateKit,
    ["givekit"] = CommandHandlers.HandleGiveKit,
    ["kitreload"] = CommandHandlers.HandleKitReload,
}

-- Event Hooks

-- Hook for player initialization - give all auto-grant kits
RegisterHook("/Script/Pal.PalPlayerCharacter:OnCompleteInitializeParameter", function(context, char)
    local playerChar = context:get()
    if playerChar and playerChar.PlayerState then
        local playerState = playerChar.PlayerState
        local playerName = GetPlayerName(playerState)

        -- Ensure player exists in our data
        KitManager:EnsurePlayerExists(playerName)

        -- Update player level
        UpdatePlayerLevel(playerName, playerState)

        -- Give all auto-grant kits if needed
        GiveAllKitsIfNeeded(playerName, playerState)
    end
end)

-- Hook for chat commands - ONLY ! prefix
RegisterHook("/Script/Pal.PalPlayerState:EnterChat_Receive", function(ctx, chat)
    local text = chat:get().Message:ToString()
    local playerState = ctx:get()
    local controller = playerState:GetPlayerController()

    -- Parse command (ONLY ! prefix)
    local cmd, rest = text:match("^!(%S+)%s*(.*)$")
    if not cmd then return end

    cmd = cmd:lower()

    -- Check if command exists
    local handler = Commands[cmd]
    if handler then
        local args = ParseArgs(rest)
        handler(CommandHandlers, controller, args)
    end
end)

-- Initialize system
print("[Advanced Kit Manager] Loading...")
print("[Advanced Kit Manager] Loaded successfully!")
print("[Advanced Kit Manager] Available commands: !kithelp")

-- Auto-save player data periodically
local function AutoSave()
    KitManager:SavePlayerData()
    ExecuteWithDelay(300000, AutoSave) -- Save every 5 minutes
end

-- Start auto-save
ExecuteWithDelay(300000, AutoSave)
